const express = require("express");
const router = express.Router();

router.get("/register", (req, res) => {
  res.render("userRegistration", {
    title: "User Registration",
    header: "User Registration",
  });
});

router.post("/register", (req, res) => {
  const { name, email, password, confirmPassword } = req.body;
  if (!name || !email || !password || !confirmPassword) {
    return res.status(400).send("Please fill all the fields");
  }
  if (password !== confirmPassword) {
    return res.status(400).send("Passwords do not match");
  }
  res.send("User registered successfully");
});

router.get("/login", (req, res) => {
  res.render("userLogin", { title: "User Login", header: "User Login" });
});

router.post("/login", (req, res) => {
  const { email, password } = req.body;
  if (!email || !password) {
    return res.status(400).send("Please fill all the fields");
  }
  res.send("User logged in successfully");
});

module.exports = router;
