const express = require("express");
const app = express();
const port = 8000;
const connectDB = require("./connection");
connectDB();
const userRouter = require("./routes/user");


// middleware
app.set("view engine", "ejs");
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

app.get("/", (req, res) => {
    res.send("Hello World!");
});
app.use("/user", userRouter);

app.listen(port, () => {
  console.log(`The application started successfully on port ${port}`);
});
