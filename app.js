const express = require("express");
const app = express();
const port = 8000;
const connectDB = require("./connection");
require("dotenv").config();
const url = process.env.MONGO_URI;
console.log(url);

// Connect to MongoDB
connectDB(url);

app.get("/", (req, res) => {
  res.send("Hello World!");
});

app.listen(port, () => {
  console.log(`The application started successfully on port ${port}`);
});
