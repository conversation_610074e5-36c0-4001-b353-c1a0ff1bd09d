const express = require("express");
const app = express();
const port = 8000;
const connectDB = require("./connection");
const dotenv = require("dotenv");
const expressLayouts = require("express-ejs-layouts");
const path = require("path");
dotenv.config();
const url = process.env.MONGO_URI;
connectDB(url);
const userRouter = require("./routes/user");







// middleware
app.set("view engine", "ejs");
app.set("views", path.join(__dirname, "views"));
app.use(expressLayouts);
app.set("layout", "base");
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

app.get("/", (req, res) => {
  res.send("Hello World!");
});
app.use("/user", userRouter);

app.listen(port, () => {
  console.log(`The application started successfully on port ${port}`);
});
